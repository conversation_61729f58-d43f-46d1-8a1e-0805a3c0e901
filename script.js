// متغيرات عامة
let map;
let profileData = {
    email: '',
    password: '',
    profileImage: ''
};

// تهيئة خريطة Google
function initMap() {
    // إحداثيات افتراضية (الرياض، السعودية)
    const defaultLocation = { lat: 24.7136, lng: 46.6753 };
    
    // إنشاء الخريطة
    map = new google.maps.Map(document.getElementById("map"), {
        zoom: 10,
        center: defaultLocation,
        mapTypeControl: true,
        streetViewControl: true,
        fullscreenControl: true,
        zoomControl: true,
        styles: [
            {
                featureType: "poi",
                elementType: "labels",
                stylers: [{ visibility: "on" }]
            }
        ]
    });

    // إضافة علامة على الموقع الافتراضي
    const marker = new google.maps.Marker({
        position: defaultLocation,
        map: map,
        title: "الموقع الحالي",
        animation: google.maps.Animation.BOUNCE
    });

    // محاولة الحصول على الموقع الحالي للمستخدم
    if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
            (position) => {
                const userLocation = {
                    lat: position.coords.latitude,
                    lng: position.coords.longitude
                };
                
                // تحديث مركز الخريطة والعلامة
                map.setCenter(userLocation);
                marker.setPosition(userLocation);
                marker.setAnimation(null);
            },
            (error) => {
                console.log("خطأ في الحصول على الموقع:", error);
            }
        );
    }
}

// إدارة النافذة المنبثقة للملف الشخصي
document.addEventListener('DOMContentLoaded', function() {
    const profileCircle = document.getElementById('profileCircle');
    const profileModal = document.getElementById('profileModal');
    const closeBtn = document.getElementById('closeBtn');
    const cancelBtn = document.getElementById('cancelBtn');
    const profileForm = document.getElementById('profileForm');
    const profilePicture = document.getElementById('profilePicture');
    const previewImage = document.getElementById('previewImage');
    const profileImage = document.getElementById('profileImage');

    // تحميل البيانات المحفوظة
    loadProfileData();

    // فتح النافذة المنبثقة
    profileCircle.addEventListener('click', function() {
        profileModal.style.display = 'block';
        document.body.style.overflow = 'hidden';
    });

    // إغلاق النافذة المنبثقة
    function closeModal() {
        profileModal.style.display = 'none';
        document.body.style.overflow = 'auto';
    }

    closeBtn.addEventListener('click', closeModal);
    cancelBtn.addEventListener('click', closeModal);

    // إغلاق النافذة عند النقر خارجها
    profileModal.addEventListener('click', function(e) {
        if (e.target === profileModal) {
            closeModal();
        }
    });

    // معاينة الصورة المختارة
    profilePicture.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                previewImage.src = e.target.result;
                profileData.profileImage = e.target.result;
            };
            reader.readAsDataURL(file);
        }
    });

    // حفظ بيانات النموذج
    profileForm.addEventListener('submit', function(e) {
        e.preventDefault();

        const email = document.getElementById('email').value;
        const password = document.getElementById('password').value;
        const confirmPassword = document.getElementById('confirmPassword').value;

        // التحقق من صحة البيانات
        if (!validateForm(email, password, confirmPassword)) {
            return;
        }

        // إظهار تأثير التحميل
        showLoadingOverlay();

        // محاكاة عملية الحفظ (يمكن استبدالها بطلب API حقيقي)
        setTimeout(() => {
            // حفظ البيانات
            profileData.email = email;
            profileData.password = password;

            // حفظ البيانات في التخزين المحلي
            saveProfileData();

            // تحديث صورة الملف الشخصي في الدائرة
            if (profileData.profileImage) {
                profileImage.src = profileData.profileImage;
            }

            // إخفاء تأثير التحميل
            hideLoadingOverlay();

            // إظهار رسالة نجاح
            showSuccessMessage();

            // إغلاق النافذة المنبثقة
            setTimeout(() => {
                closeModal();
            }, 500);
        }, 1500); // محاكاة وقت المعالجة
    });

    // التحقق من صحة النموذج
    function validateForm(email, password, confirmPassword) {
        if (!email || !password || !confirmPassword) {
            showErrorMessage('يرجى ملء جميع الحقول المطلوبة');
            return false;
        }

        if (!isValidEmail(email)) {
            showErrorMessage('يرجى إدخال بريد إلكتروني صحيح');
            return false;
        }

        if (password.length < 6) {
            showErrorMessage('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
            return false;
        }

        if (password !== confirmPassword) {
            showErrorMessage('كلمة المرور وتأكيد كلمة المرور غير متطابقتين');
            return false;
        }

        return true;
    }

    // التحقق من صحة البريد الإلكتروني
    function isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    // حفظ البيانات في التخزين المحلي
    function saveProfileData() {
        localStorage.setItem('profileData', JSON.stringify(profileData));
    }

    // تحميل البيانات من التخزين المحلي
    function loadProfileData() {
        const savedData = localStorage.getItem('profileData');
        if (savedData) {
            profileData = JSON.parse(savedData);
            
            // تحديث النموذج بالبيانات المحفوظة
            document.getElementById('email').value = profileData.email || '';
            
            if (profileData.profileImage) {
                previewImage.src = profileData.profileImage;
                profileImage.src = profileData.profileImage;
            }
        }
    }

    // إظهار رسالة نجاح
    function showSuccessMessage() {
        const message = document.createElement('div');
        message.className = 'success-message';
        message.innerHTML = `
            <div class="message-icon">✓</div>
            <div class="message-text">تم حفظ البيانات بنجاح!</div>
        `;
        message.style.cssText = `
            position: fixed;
            top: 80px;
            right: 20px;
            background: linear-gradient(135deg, #00c851 0%, #007e33 100%);
            color: white;
            padding: 20px 25px;
            border-radius: 15px;
            z-index: 3000;
            box-shadow: 0 8px 25px rgba(0, 200, 81, 0.3);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            gap: 12px;
            font-weight: 600;
            animation: slideInRight 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
            min-width: 280px;
        `;

        // إضافة أنيميشن للأيقونة
        const icon = message.querySelector('.message-icon');
        icon.style.cssText = `
            width: 24px;
            height: 24px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            animation: checkmark 0.6s ease 0.2s both;
        `;

        document.body.appendChild(message);

        setTimeout(() => {
            message.style.animation = 'slideOutRight 0.3s ease forwards';
            setTimeout(() => message.remove(), 300);
        }, 3000);
    }

    // إظهار رسالة خطأ
    function showErrorMessage(text) {
        const message = document.createElement('div');
        message.className = 'error-message';
        message.innerHTML = `
            <div class="message-icon">⚠</div>
            <div class="message-text">${text}</div>
        `;
        message.style.cssText = `
            position: fixed;
            top: 80px;
            right: 20px;
            background: linear-gradient(135deg, #ff4444 0%, #cc0000 100%);
            color: white;
            padding: 20px 25px;
            border-radius: 15px;
            z-index: 3000;
            box-shadow: 0 8px 25px rgba(255, 68, 68, 0.3);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            gap: 12px;
            font-weight: 600;
            animation: slideInRight 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
            min-width: 280px;
            max-width: 400px;
        `;

        // إضافة أنيميشن للأيقونة
        const icon = message.querySelector('.message-icon');
        icon.style.cssText = `
            width: 24px;
            height: 24px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            animation: shake 0.6s ease;
        `;

        document.body.appendChild(message);

        setTimeout(() => {
            message.style.animation = 'slideOutRight 0.3s ease forwards';
            setTimeout(() => message.remove(), 300);
        }, 4000);
    }

    // إظهار تأثير التحميل
    function showLoadingOverlay() {
        const overlay = document.createElement('div');
        overlay.className = 'loading-overlay';
        overlay.innerHTML = `
            <div class="loading-content">
                <div class="loading-spinner"></div>
                <div style="color: white; margin-top: 20px; font-size: 16px; font-weight: 600;">جاري الحفظ...</div>
            </div>
        `;
        document.body.appendChild(overlay);
    }

    // إخفاء تأثير التحميل
    function hideLoadingOverlay() {
        const overlay = document.querySelector('.loading-overlay');
        if (overlay) {
            overlay.style.animation = 'fadeOut 0.3s ease forwards';
            setTimeout(() => overlay.remove(), 300);
        }
    }

    // إضافة تأثيرات تفاعلية للحقول
    const inputs = document.querySelectorAll('.form-group input');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.style.transform = 'translateY(-2px)';
            this.parentElement.style.transition = 'transform 0.3s ease';
        });

        input.addEventListener('blur', function() {
            this.parentElement.style.transform = 'translateY(0)';
        });

        // تأثير الكتابة
        input.addEventListener('input', function() {
            if (this.value.length > 0) {
                this.style.background = 'rgba(102, 126, 234, 0.05)';
            } else {
                this.style.background = 'rgba(255, 255, 255, 0.8)';
            }
        });
    });
});

// إضافة أنيميشن CSS للرسائل والتأثيرات
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from {
            transform: translateX(100%) scale(0.8);
            opacity: 0;
        }
        to {
            transform: translateX(0) scale(1);
            opacity: 1;
        }
    }

    @keyframes slideOutRight {
        from {
            transform: translateX(0) scale(1);
            opacity: 1;
        }
        to {
            transform: translateX(100%) scale(0.8);
            opacity: 0;
        }
    }

    @keyframes checkmark {
        0% {
            transform: scale(0) rotate(0deg);
        }
        50% {
            transform: scale(1.2) rotate(180deg);
        }
        100% {
            transform: scale(1) rotate(360deg);
        }
    }

    @keyframes shake {
        0%, 100% { transform: translateX(0); }
        10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }
        20%, 40%, 60%, 80% { transform: translateX(2px); }
    }

    /* تحسين تأثيرات الإدخال */
    .form-group input:invalid {
        border-color: #ff4444;
        box-shadow: 0 0 0 3px rgba(255, 68, 68, 0.1);
    }

    .form-group input:valid {
        border-color: #00c851;
        box-shadow: 0 0 0 3px rgba(0, 200, 81, 0.1);
    }

    /* تأثير التحميل */
    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(102, 126, 234, 0.9);
        backdrop-filter: blur(10px);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 4000;
        animation: fadeIn 0.3s ease;
    }

    .loading-spinner {
        width: 60px;
        height: 60px;
        border: 4px solid rgba(255, 255, 255, 0.3);
        border-top: 4px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    @keyframes fadeOut {
        from {
            opacity: 1;
        }
        to {
            opacity: 0;
        }
    }
`;
document.head.appendChild(style);
