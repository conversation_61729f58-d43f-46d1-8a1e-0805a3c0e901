// متغيرات عامة
let map;
let profileData = {
    email: '',
    password: '',
    profileImage: ''
};

// تهيئة خريطة Google
function initMap() {
    // إحداثيات افتراضية (الرياض، السعودية)
    const defaultLocation = { lat: 24.7136, lng: 46.6753 };
    
    // إنشاء الخريطة
    map = new google.maps.Map(document.getElementById("map"), {
        zoom: 10,
        center: defaultLocation,
        mapTypeControl: true,
        streetViewControl: true,
        fullscreenControl: true,
        zoomControl: true,
        styles: [
            {
                featureType: "poi",
                elementType: "labels",
                stylers: [{ visibility: "on" }]
            }
        ]
    });

    // إضافة علامة على الموقع الافتراضي
    const marker = new google.maps.Marker({
        position: defaultLocation,
        map: map,
        title: "الموقع الحالي",
        animation: google.maps.Animation.BOUNCE
    });

    // محاولة الحصول على الموقع الحالي للمستخدم
    if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
            (position) => {
                const userLocation = {
                    lat: position.coords.latitude,
                    lng: position.coords.longitude
                };
                
                // تحديث مركز الخريطة والعلامة
                map.setCenter(userLocation);
                marker.setPosition(userLocation);
                marker.setAnimation(null);
            },
            (error) => {
                console.log("خطأ في الحصول على الموقع:", error);
            }
        );
    }
}

// إدارة النوافذ المنبثقة
document.addEventListener('DOMContentLoaded', function() {
    const profileCircle = document.getElementById('profileCircle');
    const profileModal = document.getElementById('profileModal');
    const privacyModal = document.getElementById('privacyModal');
    const closeBtn = document.getElementById('closeBtn');
    const closePrivacyBtn = document.getElementById('closePrivacyBtn');
    const closeProfileBtn = document.getElementById('closeProfileBtn');
    const cancelPrivacyBtn = document.getElementById('cancelPrivacyBtn');
    const privacyLink = document.getElementById('privacyLink');
    const privacyForm = document.getElementById('privacyForm');
    const profilePicture = document.getElementById('profilePicture');
    const previewImage = document.getElementById('previewImage');
    const profileImage = document.getElementById('profileImage');
    const displayEmail = document.getElementById('displayEmail');

    // تحميل البيانات المحفوظة
    loadProfileData();

    // فتح نافذة الملف الشخصي
    profileCircle.addEventListener('click', function() {
        profileModal.style.display = 'block';
        document.body.style.overflow = 'hidden';
        updateDisplayedInfo();
    });

    // إغلاق نافذة الملف الشخصي
    function closeProfileModal() {
        profileModal.style.display = 'none';
        document.body.style.overflow = 'auto';
    }

    // إغلاق نافذة الخصوصية
    function closePrivacyModal() {
        privacyModal.style.display = 'none';
        document.body.style.overflow = 'auto';
    }

    // فتح نافذة تعديل الخصوصية
    privacyLink.addEventListener('click', function(e) {
        e.preventDefault();
        profileModal.style.display = 'none';
        privacyModal.style.display = 'block';
        loadPrivacyData();
    });

    // أحداث إغلاق النوافذ
    closeBtn.addEventListener('click', closeProfileModal);
    closeProfileBtn.addEventListener('click', closeProfileModal);
    closePrivacyBtn.addEventListener('click', closePrivacyModal);
    cancelPrivacyBtn.addEventListener('click', closePrivacyModal);

    // إغلاق النوافذ عند النقر خارجها
    profileModal.addEventListener('click', function(e) {
        if (e.target === profileModal) {
            closeProfileModal();
        }
    });

    privacyModal.addEventListener('click', function(e) {
        if (e.target === privacyModal) {
            closePrivacyModal();
        }
    });

    // معاينة الصورة المختارة
    profilePicture.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                previewImage.src = e.target.result;
                profileData.profileImage = e.target.result;
            };
            reader.readAsDataURL(file);
        }
    });

    // حفظ بيانات الخصوصية
    privacyForm.addEventListener('submit', function(e) {
        e.preventDefault();

        const email = document.getElementById('email').value;
        const currentPassword = document.getElementById('currentPassword').value;
        const newPassword = document.getElementById('newPassword').value;
        const confirmNewPassword = document.getElementById('confirmNewPassword').value;

        // التحقق من صحة البيانات
        if (!validatePrivacyForm(email, currentPassword, newPassword, confirmNewPassword)) {
            return;
        }

        // إظهار تأثير التحميل
        showLoadingOverlay();

        // محاكاة عملية الحفظ (يمكن استبدالها بطلب API حقيقي)
        setTimeout(() => {
            // حفظ البيانات
            profileData.email = email;
            profileData.password = newPassword;

            // حفظ البيانات في التخزين المحلي
            saveProfileData();

            // تحديث صورة الملف الشخصي في الدائرة
            if (profileData.profileImage) {
                profileImage.src = profileData.profileImage;
            }

            // إخفاء تأثير التحميل
            hideLoadingOverlay();

            // إظهار رسالة نجاح
            showSuccessMessage();

            // إغلاق نافذة الخصوصية والعودة للملف الشخصي
            setTimeout(() => {
                closePrivacyModal();
                profileModal.style.display = 'block';
                updateDisplayedInfo();
            }, 500);
        }, 1500); // محاكاة وقت المعالجة
    });

    // التحقق من صحة نموذج الخصوصية
    function validatePrivacyForm(email, currentPassword, newPassword, confirmNewPassword) {
        if (!email || !currentPassword || !newPassword || !confirmNewPassword) {
            showErrorMessage('يرجى ملء جميع الحقول المطلوبة');
            return false;
        }

        if (!isValidEmail(email)) {
            showErrorMessage('يرجى إدخال بريد إلكتروني صحيح');
            return false;
        }

        // التحقق من كلمة المرور الحالية
        if (profileData.password && currentPassword !== profileData.password) {
            showErrorMessage('كلمة المرور الحالية غير صحيحة');
            return false;
        }

        if (newPassword.length < 6) {
            showErrorMessage('كلمة المرور الجديدة يجب أن تكون 6 أحرف على الأقل');
            return false;
        }

        if (newPassword !== confirmNewPassword) {
            showErrorMessage('كلمة المرور الجديدة وتأكيد كلمة المرور غير متطابقتين');
            return false;
        }

        return true;
    }

    // التحقق من صحة البريد الإلكتروني
    function isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    // حفظ البيانات في التخزين المحلي
    function saveProfileData() {
        localStorage.setItem('profileData', JSON.stringify(profileData));
    }

    // تحميل البيانات من التخزين المحلي
    function loadProfileData() {
        const savedData = localStorage.getItem('profileData');
        if (savedData) {
            profileData = JSON.parse(savedData);

            if (profileData.profileImage) {
                previewImage.src = profileData.profileImage;
                profileImage.src = profileData.profileImage;
            }
        }
    }

    // تحديث المعلومات المعروضة في الملف الشخصي
    function updateDisplayedInfo() {
        displayEmail.textContent = profileData.email || 'غير محدد';
    }

    // تحميل بيانات الخصوصية في النموذج
    function loadPrivacyData() {
        document.getElementById('email').value = profileData.email || '';
        document.getElementById('currentPassword').value = '';
        document.getElementById('newPassword').value = '';
        document.getElementById('confirmNewPassword').value = '';
    }

    // إظهار رسالة نجاح
    function showSuccessMessage() {
        const message = document.createElement('div');
        message.className = 'success-message';
        message.innerHTML = `
            <div class="message-icon">✓</div>
            <div class="message-text">تم حفظ البيانات بنجاح!</div>
        `;
        message.style.cssText = `
            position: fixed;
            top: 80px;
            right: 20px;
            background: linear-gradient(135deg, #00c851 0%, #007e33 100%);
            color: white;
            padding: 20px 25px;
            border-radius: 15px;
            z-index: 3000;
            box-shadow: 0 8px 25px rgba(0, 200, 81, 0.3);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            gap: 12px;
            font-weight: 600;
            animation: slideInRight 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
            min-width: 280px;
        `;

        // إضافة أنيميشن للأيقونة
        const icon = message.querySelector('.message-icon');
        icon.style.cssText = `
            width: 24px;
            height: 24px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            animation: checkmark 0.6s ease 0.2s both;
        `;

        document.body.appendChild(message);

        setTimeout(() => {
            message.style.animation = 'slideOutRight 0.3s ease forwards';
            setTimeout(() => message.remove(), 300);
        }, 3000);
    }

    // إظهار رسالة خطأ
    function showErrorMessage(text) {
        const message = document.createElement('div');
        message.className = 'error-message';
        message.innerHTML = `
            <div class="message-icon">⚠</div>
            <div class="message-text">${text}</div>
        `;
        message.style.cssText = `
            position: fixed;
            top: 80px;
            right: 20px;
            background: linear-gradient(135deg, #ff4444 0%, #cc0000 100%);
            color: white;
            padding: 20px 25px;
            border-radius: 15px;
            z-index: 3000;
            box-shadow: 0 8px 25px rgba(255, 68, 68, 0.3);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            gap: 12px;
            font-weight: 600;
            animation: slideInRight 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
            min-width: 280px;
            max-width: 400px;
        `;

        // إضافة أنيميشن للأيقونة
        const icon = message.querySelector('.message-icon');
        icon.style.cssText = `
            width: 24px;
            height: 24px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            animation: shake 0.6s ease;
        `;

        document.body.appendChild(message);

        setTimeout(() => {
            message.style.animation = 'slideOutRight 0.3s ease forwards';
            setTimeout(() => message.remove(), 300);
        }, 4000);
    }

    // إظهار تأثير التحميل
    function showLoadingOverlay() {
        const overlay = document.createElement('div');
        overlay.className = 'loading-overlay';
        overlay.innerHTML = `
            <div class="loading-content">
                <div class="loading-spinner"></div>
                <div style="color: white; margin-top: 20px; font-size: 16px; font-weight: 600;">جاري الحفظ...</div>
            </div>
        `;
        document.body.appendChild(overlay);
    }

    // إخفاء تأثير التحميل
    function hideLoadingOverlay() {
        const overlay = document.querySelector('.loading-overlay');
        if (overlay) {
            overlay.style.animation = 'fadeOut 0.3s ease forwards';
            setTimeout(() => overlay.remove(), 300);
        }
    }

    // إضافة تأثيرات تفاعلية للحقول
    const inputs = document.querySelectorAll('.form-group input');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.style.transform = 'translateY(-2px)';
            this.parentElement.style.transition = 'transform 0.3s ease';
        });

        input.addEventListener('blur', function() {
            this.parentElement.style.transform = 'translateY(0)';
        });

        // تأثير الكتابة
        input.addEventListener('input', function() {
            if (this.value.length > 0) {
                this.style.background = 'rgba(102, 126, 234, 0.05)';
            } else {
                this.style.background = 'rgba(255, 255, 255, 0.8)';
            }
        });
    });

    // إدارة الشريط السفلي التفاعلي
    initBottomNavigation();

    // إدارة الشريط العلوي
    initTopNavigation();
});

// إدارة الشريط السفلي
function initBottomNavigation() {
    const navItems = document.querySelectorAll('.nav-item');
    let hoverTimers = new Map();

    navItems.forEach(item => {
        const page = item.dataset.page;

        // تأثير النقر
        item.addEventListener('click', function() {
            // إزالة النشاط من جميع العناصر
            navItems.forEach(nav => nav.classList.remove('active'));

            // إضافة النشاط للعنصر المحدد
            this.classList.add('active');

            // تنفيذ الإجراء حسب الصفحة
            handleNavigation(page);
        });

        // تأثير الوقوف الطويل (5 ثوانٍ)
        item.addEventListener('mouseenter', function() {
            const timer = setTimeout(() => {
                this.classList.add('spinning');
                this.classList.add('long-hover');

                // إزالة التأثير بعد انتهاء الدوران
                setTimeout(() => {
                    this.classList.remove('spinning');
                }, 1000);
            }, 5000); // 5 ثوانٍ

            hoverTimers.set(this, timer);
        });

        item.addEventListener('mouseleave', function() {
            // إلغاء المؤقت إذا غادر المستخدم قبل 5 ثوانٍ
            const timer = hoverTimers.get(this);
            if (timer) {
                clearTimeout(timer);
                hoverTimers.delete(this);
            }

            // إزالة تأثير الوقوف الطويل
            this.classList.remove('long-hover');
        });

        // تأثير اللمس للأجهزة المحمولة
        let touchTimer;
        item.addEventListener('touchstart', function() {
            touchTimer = setTimeout(() => {
                this.classList.add('spinning');
                this.classList.add('long-hover');

                // اهتزاز خفيف إذا كان متاحاً
                if (navigator.vibrate) {
                    navigator.vibrate(100);
                }

                setTimeout(() => {
                    this.classList.remove('spinning');
                    this.classList.remove('long-hover');
                }, 1000);
            }, 5000);
        });

        item.addEventListener('touchend', function() {
            if (touchTimer) {
                clearTimeout(touchTimer);
            }
        });
    });
}

// التعامل مع التنقل
function handleNavigation(page) {
    console.log(`التنقل إلى صفحة: ${page}`);

    switch(page) {
        case 'home':
            showHomePage();
            break;
        case 'calendar':
            showCalendarPage();
            break;
        case 'add':
            showAddMemoryPage();
            break;
        case 'search':
            showSearchPage();
            break;
        case 'settings':
            showSettingsPage();
            break;
        default:
            console.log('صفحة غير معروفة');
    }
}

// وظائف الصفحات (يمكن تطويرها لاحقاً)
function showHomePage() {
    showMessage('مرحباً بك في الصفحة الرئيسية! 🏠', 'info');
}

function showCalendarPage() {
    showMessage('عرض التقويم والذكريات 📅', 'info');
}

function showAddMemoryPage() {
    showMessage('إضافة ذكرى جديدة ➕', 'success');
}

function showSearchPage() {
    showMessage('البحث في الذكريات 🔍', 'info');
}

function showSettingsPage() {
    showMessage('الإعدادات والحساب ⚙️', 'info');
}

// عرض رسالة تفاعلية
function showMessage(text, type = 'info') {
    const message = document.createElement('div');
    message.className = `nav-message ${type}`;
    message.textContent = text;

    const colors = {
        info: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        success: 'linear-gradient(135deg, #00c851 0%, #007e33 100%)',
        warning: 'linear-gradient(135deg, #ffbb33 0%, #ff8800 100%)'
    };

    message.style.cssText = `
        position: fixed;
        top: 80px;
        left: 50%;
        transform: translateX(-50%);
        background: ${colors[type]};
        color: white;
        padding: 15px 25px;
        border-radius: 25px;
        z-index: 3000;
        font-weight: 600;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        animation: slideInTop 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
    `;

    document.body.appendChild(message);

    setTimeout(() => {
        message.style.animation = 'slideOutTop 0.3s ease forwards';
        setTimeout(() => message.remove(), 300);
    }, 2500);
}

// إدارة الشريط العلوي
function initTopNavigation() {
    const mapBtn = document.getElementById('mapBtn');
    const memoriesBtn = document.getElementById('memoriesBtn');
    const mapView = document.getElementById('mapView');
    const memoriesView = document.getElementById('memoriesView');

    // التنقل إلى الخريطة
    mapBtn.addEventListener('click', function() {
        // تحديث الأزرار
        mapBtn.classList.add('active');
        memoriesBtn.classList.remove('active');

        // تحديث المحتوى
        mapView.classList.add('active');
        memoriesView.classList.remove('active');

        // إعادة تحميل الخريطة إذا لزم الأمر
        if (map) {
            google.maps.event.trigger(map, 'resize');
        }

        showMessage('عرض الخريطة 🗺️', 'info');
    });

    // التنقل إلى الذكريات
    memoriesBtn.addEventListener('click', function() {
        // تحديث الأزرار
        memoriesBtn.classList.add('active');
        mapBtn.classList.remove('active');

        // تحديث المحتوى
        memoriesView.classList.add('active');
        mapView.classList.remove('active');

        showMessage('عرض الذكريات 📸', 'info');
    });

    // إضافة تأثيرات تفاعلية للأزرار
    [mapBtn, memoriesBtn].forEach(btn => {
        let hoverTimer;

        // تأثير الوقوف الطويل (5 ثوانٍ)
        btn.addEventListener('mouseenter', function() {
            hoverTimer = setTimeout(() => {
                this.style.animation = 'spin360 1s ease-in-out';

                setTimeout(() => {
                    this.style.animation = '';
                }, 1000);
            }, 5000);
        });

        btn.addEventListener('mouseleave', function() {
            if (hoverTimer) {
                clearTimeout(hoverTimer);
            }
        });

        // تأثير اللمس للأجهزة المحمولة
        let touchTimer;
        btn.addEventListener('touchstart', function() {
            touchTimer = setTimeout(() => {
                this.style.animation = 'spin360 1s ease-in-out';

                if (navigator.vibrate) {
                    navigator.vibrate(100);
                }

                setTimeout(() => {
                    this.style.animation = '';
                }, 1000);
            }, 5000);
        });

        btn.addEventListener('touchend', function() {
            if (touchTimer) {
                clearTimeout(touchTimer);
            }
        });
    });

    // إدارة بطاقة إضافة ذكرى
    const addMemoryCard = document.querySelector('.add-memory');
    if (addMemoryCard) {
        addMemoryCard.addEventListener('click', function() {
            showAddMemoryPage();
        });
    }
}

// إضافة أنيميشن CSS للرسائل والتأثيرات
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from {
            transform: translateX(100%) scale(0.8);
            opacity: 0;
        }
        to {
            transform: translateX(0) scale(1);
            opacity: 1;
        }
    }

    @keyframes slideOutRight {
        from {
            transform: translateX(0) scale(1);
            opacity: 1;
        }
        to {
            transform: translateX(100%) scale(0.8);
            opacity: 0;
        }
    }

    @keyframes checkmark {
        0% {
            transform: scale(0) rotate(0deg);
        }
        50% {
            transform: scale(1.2) rotate(180deg);
        }
        100% {
            transform: scale(1) rotate(360deg);
        }
    }

    @keyframes shake {
        0%, 100% { transform: translateX(0); }
        10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }
        20%, 40%, 60%, 80% { transform: translateX(2px); }
    }

    /* تحسين تأثيرات الإدخال */
    .form-group input:invalid {
        border-color: #ff4444;
        box-shadow: 0 0 0 3px rgba(255, 68, 68, 0.1);
    }

    .form-group input:valid {
        border-color: #00c851;
        box-shadow: 0 0 0 3px rgba(0, 200, 81, 0.1);
    }

    /* تأثير التحميل */
    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(102, 126, 234, 0.9);
        backdrop-filter: blur(10px);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 4000;
        animation: fadeIn 0.3s ease;
    }

    .loading-spinner {
        width: 60px;
        height: 60px;
        border: 4px solid rgba(255, 255, 255, 0.3);
        border-top: 4px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    @keyframes fadeOut {
        from {
            opacity: 1;
        }
        to {
            opacity: 0;
        }
    }

    @keyframes slideInTop {
        from {
            transform: translateX(-50%) translateY(-100px);
            opacity: 0;
        }
        to {
            transform: translateX(-50%) translateY(0);
            opacity: 1;
        }
    }

    @keyframes slideOutTop {
        from {
            transform: translateX(-50%) translateY(0);
            opacity: 1;
        }
        to {
            transform: translateX(-50%) translateY(-100px);
            opacity: 0;
        }
    }
`;
document.head.appendChild(style);
