// متغيرات عامة
let map;
let profileData = {
    email: '',
    password: '',
    profileImage: ''
};

// تهيئة خريطة Google
function initMap() {
    // إحداثيات افتراضية (الرياض، السعودية)
    const defaultLocation = { lat: 24.7136, lng: 46.6753 };
    
    // إنشاء الخريطة
    map = new google.maps.Map(document.getElementById("map"), {
        zoom: 10,
        center: defaultLocation,
        mapTypeControl: true,
        streetViewControl: true,
        fullscreenControl: true,
        zoomControl: true,
        styles: [
            {
                featureType: "poi",
                elementType: "labels",
                stylers: [{ visibility: "on" }]
            }
        ]
    });

    // إضافة علامة على الموقع الافتراضي
    const marker = new google.maps.Marker({
        position: defaultLocation,
        map: map,
        title: "الموقع الحالي",
        animation: google.maps.Animation.BOUNCE
    });

    // محاولة الحصول على الموقع الحالي للمستخدم
    if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
            (position) => {
                const userLocation = {
                    lat: position.coords.latitude,
                    lng: position.coords.longitude
                };
                
                // تحديث مركز الخريطة والعلامة
                map.setCenter(userLocation);
                marker.setPosition(userLocation);
                marker.setAnimation(null);
            },
            (error) => {
                console.log("خطأ في الحصول على الموقع:", error);
            }
        );
    }
}

// إدارة النافذة المنبثقة للملف الشخصي
document.addEventListener('DOMContentLoaded', function() {
    const profileCircle = document.getElementById('profileCircle');
    const profileModal = document.getElementById('profileModal');
    const closeBtn = document.getElementById('closeBtn');
    const cancelBtn = document.getElementById('cancelBtn');
    const profileForm = document.getElementById('profileForm');
    const profilePicture = document.getElementById('profilePicture');
    const previewImage = document.getElementById('previewImage');
    const profileImage = document.getElementById('profileImage');

    // تحميل البيانات المحفوظة
    loadProfileData();

    // فتح النافذة المنبثقة
    profileCircle.addEventListener('click', function() {
        profileModal.style.display = 'block';
        document.body.style.overflow = 'hidden';
    });

    // إغلاق النافذة المنبثقة
    function closeModal() {
        profileModal.style.display = 'none';
        document.body.style.overflow = 'auto';
    }

    closeBtn.addEventListener('click', closeModal);
    cancelBtn.addEventListener('click', closeModal);

    // إغلاق النافذة عند النقر خارجها
    profileModal.addEventListener('click', function(e) {
        if (e.target === profileModal) {
            closeModal();
        }
    });

    // معاينة الصورة المختارة
    profilePicture.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                previewImage.src = e.target.result;
                profileData.profileImage = e.target.result;
            };
            reader.readAsDataURL(file);
        }
    });

    // حفظ بيانات النموذج
    profileForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const email = document.getElementById('email').value;
        const password = document.getElementById('password').value;
        const confirmPassword = document.getElementById('confirmPassword').value;

        // التحقق من صحة البيانات
        if (!validateForm(email, password, confirmPassword)) {
            return;
        }

        // حفظ البيانات
        profileData.email = email;
        profileData.password = password;
        
        // حفظ البيانات في التخزين المحلي
        saveProfileData();
        
        // تحديث صورة الملف الشخصي في الدائرة
        if (profileData.profileImage) {
            profileImage.src = profileData.profileImage;
        }

        // إظهار رسالة نجاح
        showSuccessMessage();
        
        // إغلاق النافذة المنبثقة
        closeModal();
    });

    // التحقق من صحة النموذج
    function validateForm(email, password, confirmPassword) {
        if (!email || !password || !confirmPassword) {
            showErrorMessage('يرجى ملء جميع الحقول المطلوبة');
            return false;
        }

        if (!isValidEmail(email)) {
            showErrorMessage('يرجى إدخال بريد إلكتروني صحيح');
            return false;
        }

        if (password.length < 6) {
            showErrorMessage('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
            return false;
        }

        if (password !== confirmPassword) {
            showErrorMessage('كلمة المرور وتأكيد كلمة المرور غير متطابقتين');
            return false;
        }

        return true;
    }

    // التحقق من صحة البريد الإلكتروني
    function isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    // حفظ البيانات في التخزين المحلي
    function saveProfileData() {
        localStorage.setItem('profileData', JSON.stringify(profileData));
    }

    // تحميل البيانات من التخزين المحلي
    function loadProfileData() {
        const savedData = localStorage.getItem('profileData');
        if (savedData) {
            profileData = JSON.parse(savedData);
            
            // تحديث النموذج بالبيانات المحفوظة
            document.getElementById('email').value = profileData.email || '';
            
            if (profileData.profileImage) {
                previewImage.src = profileData.profileImage;
                profileImage.src = profileData.profileImage;
            }
        }
    }

    // إظهار رسالة نجاح
    function showSuccessMessage() {
        const message = document.createElement('div');
        message.className = 'success-message';
        message.textContent = 'تم حفظ البيانات بنجاح!';
        message.style.cssText = `
            position: fixed;
            top: 80px;
            right: 20px;
            background: #4CAF50;
            color: white;
            padding: 15px 25px;
            border-radius: 5px;
            z-index: 3000;
            animation: slideInRight 0.3s ease;
        `;
        
        document.body.appendChild(message);
        
        setTimeout(() => {
            message.remove();
        }, 3000);
    }

    // إظهار رسالة خطأ
    function showErrorMessage(text) {
        const message = document.createElement('div');
        message.className = 'error-message';
        message.textContent = text;
        message.style.cssText = `
            position: fixed;
            top: 80px;
            right: 20px;
            background: #f44336;
            color: white;
            padding: 15px 25px;
            border-radius: 5px;
            z-index: 3000;
            animation: slideInRight 0.3s ease;
        `;
        
        document.body.appendChild(message);
        
        setTimeout(() => {
            message.remove();
        }, 4000);
    }
});

// إضافة أنيميشن CSS للرسائل
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
`;
document.head.appendChild(style);
