<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>خريطة Google مع الملف الشخصي</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <!-- شريط علوي مع الملف الشخصي -->
    <header class="top-bar">
        <div class="profile-container">
            <div class="profile-circle" id="profileCircle">
                <img src="https://via.placeholder.com/40x40/007bff/ffffff?text=👤" alt="الملف الشخصي" id="profileImage">
            </div>
        </div>
    </header>

    <!-- خريطة Google -->
    <div id="map"></div>

    <!-- الشريط السفلي التفاعلي -->
    <nav class="bottom-nav" id="bottomNav">
        <div class="nav-container">
            <!-- زر الرئيسية -->
            <div class="nav-item active" data-page="home">
                <div class="nav-icon">🏠</div>
                <span class="nav-label">الرئيسية</span>
                <div class="nav-indicator"></div>
            </div>

            <!-- زر التقويم -->
            <div class="nav-item" data-page="calendar">
                <div class="nav-icon">📅</div>
                <span class="nav-label">التقويم</span>
                <div class="nav-indicator"></div>
            </div>

            <!-- زر الإضافة (الوسط - أكبر) -->
            <div class="nav-item nav-add" data-page="add">
                <div class="nav-icon-large">➕</div>
                <span class="nav-label">إضافة</span>
                <div class="nav-indicator"></div>
            </div>

            <!-- زر البحث -->
            <div class="nav-item" data-page="search">
                <div class="nav-icon">🔍</div>
                <span class="nav-label">البحث</span>
                <div class="nav-indicator"></div>
            </div>

            <!-- زر الإعدادات -->
            <div class="nav-item" data-page="settings">
                <div class="nav-icon">⚙️</div>
                <span class="nav-label">الإعدادات</span>
                <div class="nav-indicator"></div>
            </div>
        </div>
    </nav>

    <!-- نافذة الملف الشخصي المنبثقة -->
    <div class="profile-modal" id="profileModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>الملف الشخصي</h2>
                <span class="close-btn" id="closeBtn">&times;</span>
            </div>

            <div class="modal-body">
                <!-- صورة الملف الشخصي -->
                <div class="form-group">
                    <label for="profilePicture">الصورة الشخصية:</label>
                    <div class="image-upload-container">
                        <img src="https://via.placeholder.com/100x100/007bff/ffffff?text=👤" alt="الصورة الشخصية" id="previewImage">
                        <input type="file" id="profilePicture" accept="image/*">
                        <label for="profilePicture" class="upload-btn">اختر صورة</label>
                    </div>
                </div>

                <!-- معلومات المستخدم -->
                <div class="user-info">
                    <div class="info-item">
                        <span class="info-label">البريد الإلكتروني:</span>
                        <span class="info-value" id="displayEmail">غير محدد</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">كلمة المرور:</span>
                        <span class="info-value">••••••••</span>
                    </div>
                </div>

                <!-- رابط تعديل الخصوصية -->
                <div class="privacy-link-container">
                    <a href="#" class="privacy-link" id="privacyLink">
                        <span class="link-icon">🔒</span>
                        <span class="link-text">تعديل الخصوصية</span>
                        <span class="link-arrow">←</span>
                    </a>
                </div>

                <!-- أزرار الإجراءات -->
                <div class="form-actions">
                    <button type="button" class="close-profile-btn" id="closeProfileBtn">إغلاق</button>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة تعديل الخصوصية -->
    <div class="privacy-modal" id="privacyModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>تعديل الخصوصية</h2>
                <span class="close-btn" id="closePrivacyBtn">&times;</span>
            </div>

            <div class="modal-body">
                <form id="privacyForm">
                    <!-- البريد الإلكتروني -->
                    <div class="form-group">
                        <label for="email">البريد الإلكتروني:</label>
                        <input type="email" id="email" placeholder="أدخل بريدك الإلكتروني" required>
                    </div>

                    <!-- كلمة المرور الحالية -->
                    <div class="form-group">
                        <label for="currentPassword">كلمة المرور الحالية:</label>
                        <input type="password" id="currentPassword" placeholder="أدخل كلمة المرور الحالية" required>
                    </div>

                    <!-- كلمة المرور الجديدة -->
                    <div class="form-group">
                        <label for="newPassword">كلمة المرور الجديدة:</label>
                        <input type="password" id="newPassword" placeholder="أدخل كلمة المرور الجديدة" required>
                    </div>

                    <!-- تأكيد كلمة المرور الجديدة -->
                    <div class="form-group">
                        <label for="confirmNewPassword">تأكيد كلمة المرور الجديدة:</label>
                        <input type="password" id="confirmNewPassword" placeholder="أعد إدخال كلمة المرور الجديدة" required>
                    </div>

                    <!-- أزرار الحفظ والإلغاء -->
                    <div class="form-actions">
                        <button type="submit" class="save-btn">حفظ التغييرات</button>
                        <button type="button" class="cancel-btn" id="cancelPrivacyBtn">إلغاء</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- تحميل مكتبة Google Maps -->
    <script async defer
        src="https://maps.googleapis.com/maps/api/js?key=YOUR_API_KEY&callback=initMap">
    </script>
    <script src="script.js"></script>
</body>
</html>
