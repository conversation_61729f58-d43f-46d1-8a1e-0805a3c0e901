/* إعدادات عامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    height: 100vh;
    overflow: hidden;
    background-color: #f5f5f5;
}

/* الشريط العلوي */
.top-bar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 60px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: 0 20px;
}

/* حاوية الملف الشخصي */
.profile-container {
    position: relative;
}

/* دائرة الملف الشخصي */
.profile-circle {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    overflow: hidden;
}

.profile-circle:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.profile-circle img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

/* خريطة Google */
#map {
    height: 100vh;
    width: 100%;
    padding-top: 60px;
}

/* النافذة المنبثقة للملف الشخصي */
.profile-modal, .privacy-modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.9) 0%, rgba(118, 75, 162, 0.9) 100%);
    backdrop-filter: blur(10px);
    animation: fadeIn 0.4s ease;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.modal-content {
    background: linear-gradient(145deg, #ffffff 0%, #f8f9ff 100%);
    margin: 3% auto;
    padding: 0;
    border-radius: 25px;
    width: 90%;
    max-width: 550px;
    box-shadow:
        0 25px 50px rgba(0, 0, 0, 0.25),
        0 0 0 1px rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.6);
    animation: slideInScale 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
    overflow: hidden;
    position: relative;
}

.modal-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #f5576c, #4facfe, #00f2fe);
    background-size: 300% 100%;
    animation: gradientShift 3s ease infinite;
}

@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

@keyframes slideInScale {
    from {
        transform: translateY(-100px) scale(0.8);
        opacity: 0;
    }
    to {
        transform: translateY(0) scale(1);
        opacity: 1;
    }
}

/* رأس النافذة المنبثقة */
.modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #667eea 100%);
    background-size: 200% 200%;
    animation: headerGradient 4s ease infinite;
    color: white;
    padding: 25px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.modal-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: shimmer 2s infinite;
}

@keyframes headerGradient {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

.modal-header h2 {
    margin: 0;
    font-size: 1.8rem;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    letter-spacing: 0.5px;
}

.close-btn {
    font-size: 32px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.close-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: rotate(90deg) scale(1.1);
    color: #ffeb3b;
    box-shadow: 0 0 20px rgba(255, 235, 59, 0.5);
}

/* جسم النافذة المنبثقة */
.modal-body {
    padding: 40px 35px;
    background: linear-gradient(145deg, #ffffff 0%, #f8f9ff 100%);
    position: relative;
}

.modal-body::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 20%, rgba(102, 126, 234, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(118, 75, 162, 0.05) 0%, transparent 50%);
    pointer-events: none;
}

/* مجموعات النموذج */
.form-group {
    margin-bottom: 25px;
    position: relative;
    z-index: 1;
}

.form-group label {
    display: block;
    margin-bottom: 10px;
    font-weight: 700;
    color: #2c3e50;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
}

.form-group label::after {
    content: '';
    position: absolute;
    bottom: -3px;
    left: 0;
    width: 30px;
    height: 2px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 1px;
}

.form-group input {
    width: 100%;
    padding: 15px 20px;
    border: 2px solid #e8ecf4;
    border-radius: 15px;
    font-size: 16px;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.form-group input:focus {
    outline: none;
    border-color: #667eea;
    background: rgba(255, 255, 255, 1);
    box-shadow:
        0 0 0 4px rgba(102, 126, 234, 0.1),
        0 8px 25px rgba(102, 126, 234, 0.15);
    transform: translateY(-2px);
}

.form-group input::placeholder {
    color: #a0a9c0;
    font-style: italic;
}

/* رفع الصورة */
.image-upload-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
    padding: 25px;
    background: linear-gradient(145deg, #ffffff 0%, #f0f4ff 100%);
    border-radius: 20px;
    border: 2px dashed #d1d9e6;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.image-upload-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.image-upload-container:hover {
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.1);
}

.image-upload-container:hover::before {
    opacity: 1;
}

#previewImage {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid #ffffff;
    box-shadow:
        0 8px 25px rgba(0, 0, 0, 0.15),
        0 0 0 2px rgba(102, 126, 234, 0.2);
    transition: all 0.3s ease;
    position: relative;
    z-index: 1;
}

#previewImage:hover {
    transform: scale(1.05);
    box-shadow:
        0 12px 35px rgba(0, 0, 0, 0.2),
        0 0 0 3px rgba(102, 126, 234, 0.3);
}

#profilePicture {
    display: none;
}

.upload-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 12px 25px;
    border-radius: 30px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: none;
    font-size: 14px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
    z-index: 1;
    overflow: hidden;
}

.upload-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.upload-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.upload-btn:hover::before {
    left: 100%;
}

/* أزرار الإجراءات */
.form-actions {
    display: flex;
    gap: 20px;
    justify-content: center;
    margin-top: 40px;
    padding-top: 25px;
    border-top: 1px solid rgba(102, 126, 234, 0.1);
    position: relative;
}

.form-actions::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 1px;
    background: linear-gradient(90deg, #667eea, #764ba2);
}

.save-btn, .cancel-btn {
    padding: 15px 35px;
    border: none;
    border-radius: 30px;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
    min-width: 140px;
}

.save-btn::before, .cancel-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s ease;
}

.save-btn {
    background: linear-gradient(135deg, #00c851 0%, #007e33 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(0, 200, 81, 0.3);
}

.save-btn:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 8px 25px rgba(0, 200, 81, 0.4);
    background: linear-gradient(135deg, #00e676 0%, #00c851 100%);
}

.save-btn:hover::before {
    left: 100%;
}

.cancel-btn {
    background: linear-gradient(135deg, #ff4444 0%, #cc0000 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(255, 68, 68, 0.3);
}

.cancel-btn:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 8px 25px rgba(255, 68, 68, 0.4);
    background: linear-gradient(135deg, #ff6666 0%, #ff4444 100%);
}

.cancel-btn:hover::before {
    left: 100%;
}

.save-btn:active, .cancel-btn:active {
    transform: translateY(-1px) scale(0.98);
}

/* معلومات المستخدم */
.user-info {
    background: linear-gradient(145deg, #f8f9ff 0%, #ffffff 100%);
    border-radius: 15px;
    padding: 25px;
    margin: 25px 0;
    border: 1px solid rgba(102, 126, 234, 0.1);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid rgba(102, 126, 234, 0.1);
}

.info-item:last-child {
    border-bottom: none;
}

.info-label {
    font-weight: 600;
    color: #2c3e50;
    font-size: 14px;
}

.info-value {
    color: #667eea;
    font-weight: 500;
    font-size: 14px;
}

/* رابط تعديل الخصوصية */
.privacy-link-container {
    margin: 30px 0;
    text-align: center;
}

.privacy-link {
    display: inline-flex;
    align-items: center;
    gap: 12px;
    padding: 15px 25px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-decoration: none;
    border-radius: 25px;
    font-weight: 600;
    font-size: 16px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    position: relative;
    overflow: hidden;
}

.privacy-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
}

.privacy-link:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    text-decoration: none;
    color: white;
}

.privacy-link:hover::before {
    left: 100%;
}

.link-icon {
    font-size: 18px;
}

.link-text {
    font-size: 16px;
}

.link-arrow {
    font-size: 18px;
    transition: transform 0.3s ease;
}

.privacy-link:hover .link-arrow {
    transform: translateX(5px);
}

/* زر إغلاق الملف الشخصي */
.close-profile-btn {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    color: white;
    padding: 15px 35px;
    border: none;
    border-radius: 30px;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    min-width: 140px;
    box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
}

.close-profile-btn:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 8px 25px rgba(108, 117, 125, 0.4);
    background: linear-gradient(135deg, #5a6268 0%, #343a40 100%);
}

/* تصميم متجاوب */
@media (max-width: 768px) {
    .modal-content {
        width: 95%;
        margin: 5% auto;
        border-radius: 20px;
    }

    .modal-header {
        padding: 20px 25px;
    }

    .modal-header h2 {
        font-size: 1.5rem;
    }

    .modal-body {
        padding: 30px 25px;
    }

    .form-actions {
        flex-direction: column;
        gap: 15px;
    }

    .save-btn, .cancel-btn, .close-profile-btn {
        width: 100%;
        padding: 18px 35px;
    }

    #previewImage {
        width: 100px;
        height: 100px;
    }

    .image-upload-container {
        padding: 20px;
    }

    .user-info {
        padding: 20px;
    }

    .info-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
        padding: 12px 0;
    }

    .privacy-link {
        padding: 12px 20px;
        font-size: 14px;
    }
}

@media (max-width: 480px) {
    .modal-content {
        width: 98%;
        margin: 2% auto;
        border-radius: 15px;
    }

    .modal-header {
        padding: 15px 20px;
    }

    .modal-header h2 {
        font-size: 1.3rem;
    }

    .modal-body {
        padding: 25px 20px;
    }

    .form-group input {
        padding: 12px 15px;
        font-size: 14px;
    }

    #previewImage {
        width: 80px;
        height: 80px;
    }

    .upload-btn {
        padding: 10px 20px;
        font-size: 12px;
    }

    .user-info {
        padding: 15px;
    }

    .info-item {
        padding: 10px 0;
    }

    .info-label, .info-value {
        font-size: 13px;
    }

    .privacy-link {
        padding: 10px 18px;
        font-size: 13px;
        gap: 8px;
    }

    .link-icon, .link-arrow {
        font-size: 16px;
    }
}
